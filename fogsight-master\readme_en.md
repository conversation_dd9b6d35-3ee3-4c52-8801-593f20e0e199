# Fogsight [**English**](./readme_en.md) | [**中文**](./readme.md)

<p align="center">
  <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/logos/fogsight_logo_white_bg.png"
       alt="Fogsight Logo"
       width="300">
</p>

**Fogsight is an animation‑generation agent powered by large‑language models (LLMs).  
Give it an abstract concept or a single word and it will transform it into a high‑quality, vivid animation.**

Once Fogsight is deployed locally, simply enter a word and click **Generate** to watch the animation appear.

<p align="center">
  <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/1.png"
       alt="UI Screenshot"
       width="550">
</p>

We designed an intuitive *Language User Interface* (LUI) so you can **easily edit or refine the generated animation—“say it and it happens.”**

*Fogsight* means **“a concrete shape within fuzzy intelligence.”**

## Animation Samples

Below are animations generated by Fogsight AI. Click any thumbnail to view the video.

<table>
  <tr>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1PXgKzBEyN">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/entropy_increase_thumbnail.png" width="350"><br>
        <strong>The Law of Increasing Entropy (Physics)</strong><br>
        <em>Input: 熵增定律</em>
      </a>
    </td>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1yXgKzqE42">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/euler_formula_thumbnail.png" width="350"><br>
        <strong>Euler’s Polyhedron Formula (Mathematics)</strong><br>
        <em>Input: 欧拉定理</em>
      </a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1sQgKzMEox">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/bubble_sort_thumbnail.png" width="350"><br>
        <strong>Bubble Sort (Computer Science)</strong><br>
        <em>Input: 冒泡排序</em>
      </a>
    </td>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1yQgKzMEo6">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/affordance_thumbnail.png" width="350"><br>
        <strong>Affordance (Design)</strong><br>
        <em>Input: affordance in design</em>
      </a>
    </td>
  </tr>
</table>

## Key Features

* **Concept‑to‑Vision** – Enter a topic and Fogsight delivers a complete narrative animation with bilingual narration and cinematic visuals.  
* **Intelligent Orchestration** – An LLM drives the entire pipeline. From script to visual elements and motion, the AI composes everything in a single pass.  
* **Language User Interface (LUI)** – Iterate and fine‑tune through multi‑turn dialogue until the artwork matches your vision perfectly.  

## Quick Start

### Requirements

* Python 3.9+  
* A modern web browser (Chrome, Firefox, Edge, …)  
* An LLM API key. We recommend **Google Gemini 2.5**.

### Installation & Run

1. **Clone the repository**
   ```bash
   git clone https://github.com/fogsightai/fogsight.git
   cd fogsight
   ```

2. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

3. **Configure your API key**

   ```bash
   cp demo-credentials.json credentials.json
   # Edit credentials.json and fill in API_KEY, BASE_URL, and MODEL
   
   # OpenRouter configuration example:
   # {
   #   "API_KEY": "sk-or-v1-YourOpenRouterKey",
   #   "BASE_URL": "https://openrouter.ai/api/v1",
   #   "MODEL": "anthropic/claude-3-sonnet-20240229"
   # }
   
   # Gemini configuration example:
   # {
   #   "API_KEY": "YourGeminiKey",
   #   "BASE_URL": "",
   #   "MODEL": "gemini-2.5-pro"
   # }
   ```

4. **One‑click launch**

   ```bash
   python start_fogsight.py
   # Your browser will open at http://127.0.0.1:8000
   ```

5. **Create!**
   Enter a topic (e.g. “Bubble Sort”) in the page and wait for the result.

### Docker Setup

If you prefer using Docker, follow these steps:

1. **Ensure Docker is installed**
   Make sure Docker and docker-compose are installed on your system.

2. **Clone the repository**
   ```bash
   git clone https://github.com/fogsightai/fogsight.git
   cd fogsight
   ```

3. **Configure your API key**
   ```bash
   cp demo-credentials.json credentials.json
   # Edit credentials.json and fill in API_KEY, BASE_URL, and MODEL (same as above)
   ```

4. **Launch with Docker Compose**
   ```bash
   # Use default port 8000
   docker-compose up -d
   
   # Or specify custom port (e.g., 3000)
   HOST_PORT=3000 docker-compose up -d
   ```

5. **Access the application**
   Open your browser and visit `http://localhost:8000` (or your specified port)

6. **Stop the service**
   ```bash
   docker-compose down
   ```

## OpenRouter Support

Fogsight fully supports OpenRouter, allowing you to use various excellent large language models:

### Supported Model Examples:
- **Anthropic Claude**: `anthropic/claude-3-sonnet-20240229`, `anthropic/claude-3-opus-20240229`
- **OpenAI GPT**: `openai/gpt-4-turbo`, `openai/gpt-3.5-turbo`
- **Google Gemini**: `google/gemini-pro-1.5`, `google/gemini-pro`
- **Meta Llama**: `meta-llama/llama-3-70b-instruct`
- **Other models**: Visit [OpenRouter Models](https://openrouter.ai/models) for the complete list

### Configuration:
1. Register at [OpenRouter](https://openrouter.ai/) and get your API key
2. Edit `credentials.json`:
   ```json
   {
     "API_KEY": "sk-or-v1-YourOpenRouterKey",
     "BASE_URL": "https://openrouter.ai/api/v1",
     "MODEL": "anthropic/claude-3-sonnet-20240229"
   }
   ```
3. Launch the application and use your specified model

### Advantages:
- 🌟 **Multiple Model Options**: Access various top-tier models through one interface
- 💰 **Transparent Pricing**: Usually more competitive than official APIs
- 🚀 **Seamless Switching**: Just modify the MODEL field in the configuration file
## Contact / Community

Join the discussion via **[this link](https://fogsightai.feishu.cn/wiki/WvODwyUr1iSAe0kEyKfcpqvynGc?from=from_copylink)**.

## Contributors

### Academia

* [@taited](https://taited.github.io/) – PhD Student, CUHK (Shenzhen)
* [@yjydya](https://github.com/ydyjya) – PhD Student, Nanyang Technological University

### WaytoAGI Community

* [@richkatchen / 陈财猫](https://okjk.co/enodyA)
* [@kk](https://okjk.co/zC8myE)

### Index Future Lab

* [Lin He](https://github.com/zerohe2001)

### AI Explorers

* [Xiaodao Huang](https://okjk.co/CkFav6)

### Independent Developers & AI Artists

* [@shuyan-5200](https://github.com/shuyan-5200)
* [Ruyue Wang](https://github.com/Moonywang)
* [@Jack‑the‑Builder](https://github.com/Jack-the-Builder)
* [@xiayurain95](https://github.com/xiayurain95)
* [@Lixin Cai](https://github.com/Lixin-Cai)

## License

This project is released under the **MIT License**.
If you cite this project with attribution and a link back, we’ll be very grateful 😊.

*Fogsight is part of the WaytoAGI open‑source programme — empowering more people through AI.*
